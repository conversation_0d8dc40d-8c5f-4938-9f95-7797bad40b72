# Generated by Django 5.2.4 on 2025-07-08 23:40

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Texte',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('contenu_original', models.TextField()),
                ('contenu_corrige', models.TextField(blank=True, null=True)),
                ('date_soumission', models.DateTimeField(auto_now_add=True)),
                ('statut_correction', models.CharField(default='non corrigé', max_length=50)),
                ('utilisateur', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='textes', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
