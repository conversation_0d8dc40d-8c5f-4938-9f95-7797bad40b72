"""
Service de correction orthographique et grammaticale amélioré pour le français.
Combine plusieurs outils de correction pour de meilleurs résultats.
"""

import language_tool_python
import re
import logging

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class FrenchCorrectionService:
    """Service de correction orthographique et grammaticale pour le français."""
    
    def __init__(self):
        """Initialise les outils de correction."""
        try:
            # LanguageTool pour la grammaire et l'orthographe
            self.language_tool = language_tool_python.LanguageTool('fr')
            
            # Dictionnaire de corrections communes en français
            self.common_corrections = {
                # Erreurs courantes d'orthographe
                'dévelopement': 'développement',
                'déveloper': 'développer',
                'dévelopeur': 'développeur',
                'dévelopé': 'développé',
                'dévelopée': 'développée',
                'dévelopés': 'développés',
                'dévelopées': 'développées',
                'dévelopant': 'développant',
                'dévelopante': 'développante',
                'dévelopants': 'développants',
                'dévelopantes': 'développantes',
                'dévelopement': 'développement',
                'dévelopements': 'développements',
                'dévelopeur': 'développeur',
                'dévelopeurs': 'développeurs',
                'dévelopeuse': 'développeuse',
                'dévelopeuses': 'développeuses',
                
                # Erreurs de frappe communes
                'teh': 'the',
                'adn': 'and',
                'hte': 'the',
                'taht': 'that',
                'wihch': 'which',
                'recieve': 'receive',
                'seperate': 'separate',
                'definately': 'definitely',
                'occured': 'occurred',
                'begining': 'beginning',
                'untill': 'until',
                'sucessful': 'successful',
                'neccessary': 'necessary',
                'accomodate': 'accommodate',
                'embarass': 'embarrass',
                'maintainance': 'maintenance',
                'independant': 'independent',
                'existance': 'existence',
                'persistance': 'persistence',
                'resistence': 'resistance',
                'consistant': 'consistent',
                'occassion': 'occasion',
                'recomend': 'recommend',
                'commited': 'committed',
                'prefered': 'preferred',
                'transfered': 'transferred',
                'refered': 'referred',
                'occuring': 'occurring',
                'begining': 'beginning',
                'writting': 'writing',
                'runing': 'running',
                'stoping': 'stopping',
                'geting': 'getting',
                'puting': 'putting',
                'cuting': 'cutting',
                'hiting': 'hitting',
                'siting': 'sitting',
                'biting': 'biting',
                'wraping': 'wrapping',
                'droping': 'dropping',
                'shoping': 'shopping',
                'planing': 'planning',
                'baning': 'banning',
                'caning': 'canning',
                'faning': 'fanning',
                'maning': 'manning',
                'pining': 'pinning',
                'tining': 'tinning',
                'wining': 'winning',
                'dinning': 'dining',
                'grinning': 'grinning',
                'spinning': 'spinning',
                'thinning': 'thinning',
                'winning': 'winning',
                
                # Erreurs françaises courantes
                'dévellopement': 'développement',
                'dévelloper': 'développer',
                'dévellopeur': 'développeur',
                'dévellopé': 'développé',
                'dévellopée': 'développée',
                'dévellopés': 'développés',
                'dévellopées': 'développées',
                'dévellopant': 'développant',
                'dévellopante': 'développante',
                'dévellopants': 'développants',
                'dévellopantes': 'développantes',
                'dévellopement': 'développement',
                'dévellopements': 'développements',
                'dévellopeur': 'développeur',
                'dévellopeurs': 'développeurs',
                'dévellopeuse': 'développeuse',
                'dévellopeuses': 'développeuses',
                'apartement': 'appartement',
                'apartements': 'appartements',
                'apartenir': 'appartenir',
                'apartient': 'appartient',
                'apartenait': 'appartenait',
                'apartiendra': 'appartiendra',
                'aparition': 'apparition',
                'aparitions': 'apparitions',
                'aparaitre': 'apparaître',
                'aparait': 'apparaît',
                'aparaissait': 'apparaissait',
                'aparaitra': 'apparaîtra',
                'aparence': 'apparence',
                'aparences': 'apparences',
                'aparent': 'apparent',
                'aparente': 'apparente',
                'aparents': 'apparents',
                'aparentes': 'apparentes',
                'aparemment': 'apparemment',
                'apeler': 'appeler',
                'apelle': 'appelle',
                'apelait': 'appelait',
                'apelera': 'appellera',
                'apel': 'appel',
                'apels': 'appels',
                'apelé': 'appelé',
                'apelée': 'appelée',
                'apelés': 'appelés',
                'apelées': 'appelées',
                'apelant': 'appelant',
                'apelante': 'appelante',
                'apelants': 'appelants',
                'apelantes': 'appelantes',
                'apelation': 'appellation',
                'apelations': 'appellations',
                'aprendre': 'apprendre',
                'aprend': 'apprend',
                'aprenait': 'apprenait',
                'aprendra': 'apprendra',
                'apris': 'appris',
                'aprise': 'apprise',
                'aprises': 'apprises',
                'aprenant': 'apprenant',
                'aprenante': 'apprenante',
                'aprenants': 'apprenants',
                'aprenantes': 'apprenantes',
                'aprentissage': 'apprentissage',
                'aprentissages': 'apprentissages',
                'aprenti': 'apprenti',
                'aprentie': 'apprentie',
                'aprentis': 'apprentis',
                'aprenties': 'apprenties',
                'aprocher': 'approcher',
                'aproche': 'approche',
                'aprochait': 'approchait',
                'aprochera': 'approchera',
                'aproché': 'approché',
                'aprochée': 'approchée',
                'aprochés': 'approchés',
                'aprochées': 'approchées',
                'aprochant': 'approchant',
                'aprochante': 'approchante',
                'aprochants': 'approchants',
                'aprochantes': 'approchantes',
                'aprouver': 'approuver',
                'aprouve': 'approuve',
                'aprouvait': 'approuvait',
                'aprouvera': 'approuvera',
                'aprouvé': 'approuvé',
                'aprouvée': 'approuvée',
                'aprouvés': 'approuvés',
                'aprouvées': 'approuvées',
                'aprouvant': 'approuvant',
                'aprouvante': 'approuvante',
                'aprouvants': 'approuvants',
                'aprouvantes': 'approuvantes',
                'aprobation': 'approbation',
                'aprobations': 'approbations',
                'aquérir': 'acquérir',
                'aquiert': 'acquiert',
                'aquérait': 'acquerrait',
                'aquerra': 'acquerra',
                'aquis': 'acquis',
                'aquise': 'acquise',
                'aquises': 'acquises',
                'aquérant': 'acquérant',
                'aquérante': 'acquérante',
                'aquérants': 'acquérants',
                'aquérantes': 'acquérantes',
                'aquisition': 'acquisition',
                'aquisitions': 'acquisitions',
                'aquéreur': 'acquéreur',
                'aquéreurs': 'acquéreurs',
                'aquéreuse': 'acquéreuse',
                'aquéreuses': 'acquéreuses',
                'reçevoir': 'recevoir',
                'reçoit': 'reçoit',
                'reçevait': 'recevait',
                'reçevra': 'recevra',
                'reçu': 'reçu',
                'reçue': 'reçue',
                'reçus': 'reçus',
                'reçues': 'reçues',
                'reçevant': 'recevant',
                'reçevante': 'recevante',
                'reçevants': 'recevants',
                'reçevantes': 'recevantes',
                'réception': 'réception',
                'réceptions': 'réceptions',
                'récepteur': 'récepteur',
                'récepteurs': 'récepteurs',
                'réceptrice': 'réceptrice',
                'réceptrices': 'réceptrices',
                'réceptif': 'réceptif',
                'réceptive': 'réceptive',
                'réceptifs': 'réceptifs',
                'réceptives': 'réceptives',
                'réceptivité': 'réceptivité',
                'réceptivités': 'réceptivités',
                'réceptacle': 'réceptacle',
                'réceptacles': 'réceptacles',
                'réceptionniste': 'réceptionniste',
                'réceptionnistes': 'réceptionnistes',
                'réceptionner': 'réceptionner',
                'réceptionne': 'réceptionne',
                'réceptionnait': 'réceptionnait',
                'réceptionnera': 'réceptionnera',
                'réceptionné': 'réceptionné',
                'réceptionnée': 'réceptionnée',
                'réceptionnés': 'réceptionnés',
                'réceptionnées': 'réceptionnées',
                'réceptionnant': 'réceptionnant',
                'réceptionnante': 'réceptionnante',
                'réceptionnants': 'réceptionnants',
                'réceptionnantes': 'réceptionnantes',
            }
            
            logger.info("Service de correction français initialisé avec succès")
            
        except Exception as e:
            logger.error(f"Erreur lors de l'initialisation du service de correction: {e}")
            raise
    
    def correct_text(self, text):
        """
        Corrige un texte en utilisant plusieurs méthodes de correction.
        
        Args:
            text (str): Le texte à corriger
            
        Returns:
            str: Le texte corrigé
        """
        if not text or not text.strip():
            return text
            
        try:
            # Étape 1: Corrections communes prédéfinies
            corrected_text = self._apply_common_corrections(text)
            
            # Étape 2: Correction avec LanguageTool (grammaire et orthographe)
            corrected_text = self._apply_language_tool_correction(corrected_text)

            # Étape 3: Nettoyage final
            corrected_text = self._final_cleanup(corrected_text)
            
            logger.info(f"Correction terminée. Longueur originale: {len(text)}, Longueur corrigée: {len(corrected_text)}")
            
            return corrected_text
            
        except Exception as e:
            logger.error(f"Erreur lors de la correction du texte: {e}")
            # En cas d'erreur, retourner le texte original
            return text
    
    def _apply_common_corrections(self, text):
        """Applique les corrections communes prédéfinies."""
        corrected_text = text
        
        for incorrect, correct in self.common_corrections.items():
            # Correction sensible à la casse
            corrected_text = re.sub(r'\b' + re.escape(incorrect) + r'\b', correct, corrected_text)
            
            # Correction pour la première lettre en majuscule
            if incorrect[0].islower():
                incorrect_capitalized = incorrect.capitalize()
                correct_capitalized = correct.capitalize()
                corrected_text = re.sub(r'\b' + re.escape(incorrect_capitalized) + r'\b', 
                                      correct_capitalized, corrected_text)
        
        return corrected_text
    
    def _apply_language_tool_correction(self, text):
        """Applique la correction avec LanguageTool."""
        try:
            matches = self.language_tool.check(text)
            
            # Filtrer les matches pour éviter les corrections trop agressives
            filtered_matches = []
            for match in matches:
                # Ignorer certains types d'erreurs qui peuvent être des faux positifs
                if self._should_apply_match(match):
                    filtered_matches.append(match)
            
            corrected_text = language_tool_python.utils.correct(text, filtered_matches)
            return corrected_text
            
        except Exception as e:
            logger.warning(f"Erreur avec LanguageTool: {e}")
            return text
    
    def _should_apply_match(self, match):
        """Détermine si une correction LanguageTool doit être appliquée."""
        # Ignorer les corrections de noms propres
        if 'UPPERCASE' in match.ruleId:
            return False
            
        # Ignorer les corrections de ponctuation trop strictes
        if 'PUNCTUATION' in match.ruleId and len(match.replacements) == 0:
            return False
            
        # Appliquer les corrections d'orthographe et de grammaire
        return True
    

    
    def _final_cleanup(self, text):
        """Effectue un nettoyage final du texte."""
        # Corriger les espaces multiples
        text = re.sub(r'\s+', ' ', text)
        
        # Corriger les espaces avant la ponctuation
        text = re.sub(r'\s+([,.!?;:])', r'\1', text)
        
        # Corriger les espaces après les guillemets ouvrants et avant les guillemets fermants
        text = re.sub(r'"\s+', '"', text)
        text = re.sub(r'\s+"', '"', text)
        
        # Supprimer les espaces en début et fin
        text = text.strip()
        
        return text
    
    def get_correction_statistics(self, original_text, corrected_text):
        """
        Calcule des statistiques sur la correction effectuée.
        
        Args:
            original_text (str): Le texte original
            corrected_text (str): Le texte corrigé
            
        Returns:
            dict: Dictionnaire contenant les statistiques
        """
        original_words = len(original_text.split())
        corrected_words = len(corrected_text.split())
        
        # Calculer le nombre de changements
        original_chars = len(original_text)
        corrected_chars = len(corrected_text)
        
        # Calculer le pourcentage de similarité
        similarity = 1.0
        if original_chars > 0:
            similarity = 1 - (abs(corrected_chars - original_chars) / original_chars)
        
        return {
            'original_words': original_words,
            'corrected_words': corrected_words,
            'original_chars': original_chars,
            'corrected_chars': corrected_chars,
            'similarity_percentage': round(similarity * 100, 2),
            'changes_made': original_text != corrected_text
        }
    
    def __del__(self):
        """Ferme les ressources lors de la destruction de l'objet."""
        try:
            if hasattr(self, 'language_tool'):
                self.language_tool.close()
        except:
            pass
