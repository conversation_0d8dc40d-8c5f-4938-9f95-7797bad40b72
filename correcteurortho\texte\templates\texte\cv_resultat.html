{% extends 'texte/base.html' %}

{% block title %}Résultat Correction CV - Correcteur Français{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-10">
        <!-- Success Header -->
        <div class="text-center mb-4">
            <div class="feature-icon mx-auto" style="background: linear-gradient(135deg, var(--success-color) 0%, #10b981 100%);">
                <i class="bi bi-file-earmark-check"></i>
            </div>
            <h1 class="display-6 fw-bold text-success mb-2">CV Corrigé avec Succès !</h1>
            <p class="lead text-muted">Votre CV a été analysé et corrigé automatiquement</p>
        </div>

        <!-- CV Comparison -->
        <div class="row">
            <!-- Original CV -->
            <div class="col-lg-6 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-warning text-dark">
                        <h5 class="mb-0">
                            <i class="bi bi-file-earmark me-2"></i>
                            CV Original
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="text-comparison text-original" style="max-height: 400px; overflow-y: auto;">
                            <pre class="mb-0" style="white-space: pre-wrap; font-family: inherit;">{{ texte_original }}</pre>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Corrected CV -->
            <div class="col-lg-6 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-file-earmark-check me-2"></i>
                            CV Corrigé
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="text-comparison text-corrected" style="max-height: 400px; overflow-y: auto;">
                            <pre class="mb-0" style="white-space: pre-wrap; font-family: inherit;">{{ texte_corrige }}</pre>
                        </div>

                        <!-- Download Form -->
                        <div class="mt-3">
                            <form method="get" action="{% url 'telecharger_texte' %}" class="d-inline">
                                <input type="hidden" name="texte" value="{{ texte_corrige|escape }}">
                                <button type="submit" class="btn btn-success">
                                    <i class="bi bi-download me-2"></i>
                                    Télécharger le CV corrigé
                                </button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="text-center mt-4">
            <div class="d-flex flex-wrap justify-content-center gap-3">
                <a href="{% url 'cv_upload' %}" class="btn btn-primary btn-lg">
                    <i class="bi bi-file-earmark-plus me-2"></i>
                    Corriger un autre CV
                </a>

                <a href="{% url 'soumettre_texte' %}" class="btn btn-outline-primary btn-lg">
                    <i class="bi bi-pencil-square me-2"></i>
                    Corriger du texte
                </a>

                <a href="{% url 'historique' %}" class="btn btn-outline-secondary btn-lg">
                    <i class="bi bi-clock-history me-2"></i>
                    Voir l'historique
                </a>
            </div>
        </div>

        <!-- CV Statistics -->
        <div class="row mt-5">
            <div class="col-md-12">
                <div class="stats-card">
                    <h5><i class="bi bi-bar-chart me-2"></i>Analyse du CV</h5>
                    <div class="row mt-3">
                        <div class="col-md-3">
                            <h6>Mots Totaux</h6>
                            <h4>{{ texte_original|wordcount }}</h4>
                        </div>
                        <div class="col-md-3">
                            <h6>Caractères</h6>
                            <h4>{{ texte_original|length }}</h4>
                        </div>
                        <div class="col-md-3">
                            <h6>Lignes</h6>
                            <h4>{{ texte_original|linebreaks|length }}</h4>
                        </div>
                        <div class="col-md-3">
                            <h6>Statut</h6>
                            <h4><i class="bi bi-check-circle"></i> Corrigé</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
