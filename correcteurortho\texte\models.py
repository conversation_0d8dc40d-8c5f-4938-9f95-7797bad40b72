from django.db import models
from django.contrib.auth.models import User

class Texte(models.Model):
    utilisateur = models.ForeignKey(User, on_delete=models.CASCADE, related_name='textes')
    contenu_original = models.TextField()
    contenu_corrige = models.TextField(blank=True, null=True)
    date_soumission = models.DateTimeField(auto_now_add=True)
    statut_correction = models.CharField(max_length=50, default='non corrigé')

    def __str__(self):
        return f"Texte #{self.id} - {self.utilisateur.username}"

class Correction(models.Model):
    texte_original = models.TextField()
    texte_corrige = models.TextField()
    date_correction = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"Correction du {self.date_correction.strftime('%Y-%m-%d %H:%M:%S')}"
