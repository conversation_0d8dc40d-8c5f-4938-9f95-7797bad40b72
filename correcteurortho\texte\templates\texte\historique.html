{% extends 'texte/base.html' %}

{% block title %}Historique des Corrections - Correcteur Français{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <!-- Header -->
        <div class="text-center mb-5">
            <div class="feature-icon mx-auto">
                <i class="bi bi-clock-history"></i>
            </div>
            <h1 class="display-5 fw-bold text-primary mb-3">Historique des Corrections</h1>
            <p class="lead text-muted">
                Consultez toutes vos corrections précédentes
            </p>
        </div>

        <!-- Actions Bar -->
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h4 class="mb-0">
                <i class="bi bi-list-ul me-2"></i>
                Toutes les corrections
                <span class="badge bg-primary">{{ corrections|length }}</span>
            </h4>
            <div>
                <a href="{% url 'export_csv' %}" class="btn btn-outline-success">
                    <i class="bi bi-file-earmark-spreadsheet me-2"></i>
                    Exporter CSV
                </a>
                <a href="{% url 'soumettre_texte' %}" class="btn btn-primary">
                    <i class="bi bi-plus-circle me-2"></i>
                    Nouvelle correction
                </a>
            </div>
        </div>

        <!-- Corrections Table -->
        <div class="card">
            <div class="card-body p-0">
                {% if corrections %}
                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="table-dark">
                            <tr>
                                <th style="width: 15%;">
                                    <i class="bi bi-calendar me-2"></i>
                                    Date
                                </th>
                                <th style="width: 40%;">
                                    <i class="bi bi-file-text me-2"></i>
                                    Texte Original
                                </th>
                                <th style="width: 40%;">
                                    <i class="bi bi-check-circle me-2"></i>
                                    Texte Corrigé
                                </th>
                                <th style="width: 5%;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for correction in corrections %}
                            <tr>
                                <td class="align-middle">
                                    <small class="text-muted">
                                        {{ correction.date_correction|date:"d/m/Y" }}<br>
                                        {{ correction.date_correction|date:"H:i" }}
                                    </small>
                                </td>
                                <td class="align-middle">
                                    <div style="max-height: 100px; overflow-y: auto;">
                                        <small>{{ correction.texte_original|truncatewords:20 }}</small>
                                    </div>
                                </td>
                                <td class="align-middle">
                                    <div style="max-height: 100px; overflow-y: auto;">
                                        <small>{{ correction.texte_corrige|truncatewords:20 }}</small>
                                    </div>
                                </td>
                                <td class="align-middle text-center">
                                    <a href="{% url 'telecharger_texte' %}?texte={{ correction.texte_corrige|urlencode }}"
                                       class="btn btn-sm btn-outline-primary"
                                       title="Télécharger">
                                        <i class="bi bi-download"></i>
                                    </a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <!-- Empty State -->
                <div class="text-center py-5">
                    <div class="feature-icon mx-auto mb-3" style="background: #e2e8f0; color: #64748b;">
                        <i class="bi bi-inbox"></i>
                    </div>
                    <h5 class="text-muted">Aucune correction enregistrée</h5>
                    <p class="text-muted">Commencez par corriger votre premier texte !</p>
                    <a href="{% url 'soumettre_texte' %}" class="btn btn-primary">
                        <i class="bi bi-plus-circle me-2"></i>
                        Première correction
                    </a>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- Statistics -->
        {% if corrections %}
        <div class="row mt-5">
            <div class="col-md-4">
                <div class="stats-card text-center">
                    <h5><i class="bi bi-file-text me-2"></i>Total Corrections</h5>
                    <h2>{{ corrections|length }}</h2>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-card text-center">
                    <h5><i class="bi bi-calendar-week me-2"></i>Cette Semaine</h5>
                    <h2>{{ corrections|length }}</h2>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stats-card text-center">
                    <h5><i class="bi bi-graph-up me-2"></i>Moyenne/Jour</h5>
                    <h2>{{ corrections|length }}</h2>
                </div>
            </div>
        </div>
        {% endif %}
    </div>
</div>
{% endblock %}
