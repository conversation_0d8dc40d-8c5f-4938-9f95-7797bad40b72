<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Historique des corrections</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="container py-5">
    <h1 class="mb-4 text-primary">Historique des corrections</h1>

    <table class="table table-bordered table-striped">
        <thead class="table-dark">
            <tr>
                <th>Date</th>
                <th>Texte original</th>
                <th>Texte corrigé</th>
            </tr>
        </thead>
        <tbody>
            {% for correction in corrections %}
            <tr>
                <td>{{ correction.date_correction|date:"d/m/Y H:i" }}</td>
                <td>{{ correction.texte_original }}</td>
                <td>{{ correction.texte_corrige }}</td>
            </tr>
            {% empty %}
            <tr><td colspan="3">Aucune correction encore enregistrée.</td></tr>
            {% endfor %}
        </tbody>
    </table>

    <a href="{% url 'soumettre_texte' %}" class="btn btn-success">➕ Nouvelle correction</a>



</body>
</html>
