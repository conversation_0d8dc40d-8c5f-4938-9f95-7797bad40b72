<!-- templates/texte/resultat.html -->
<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <title>Résultat</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
</head>
<body class="container py-5">
    <h1 class="text-success mb-4">Résultat de la correction</h1>

    <div class="mb-3">
        <h4>Texte original :</h4>
        <p class="border p-3 bg-light">{{ texte_original }}</p>
    </div>

    <div class="mb-4">
        <h4>Texte corrigé :</h4>
        <p class="border p-3 bg-white">{{ texte_corrige }}</p>
        <!-- ✅ Bouton de téléchargement -->
        <a href="{% url 'telecharger_texte' %}?texte={{ texte_corrige|urlencode }}" class="btn btn-outline-secondary mt-2">
            📄 Télécharger le texte corrigé
        </a>
    </div>

    <a href="{% url 'soumettre_texte' %}" class="btn btn-primary">Corriger un autre texte</a>
    <a href="{% url 'export_csv' %}">📥 Télécharger toutes les corrections (CSV)</a>

</body>
</html>
