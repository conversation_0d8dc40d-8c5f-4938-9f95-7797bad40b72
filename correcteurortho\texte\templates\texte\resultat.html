{% extends 'texte/base.html' %}

{% block title %}Résultat de la Correction - Correcteur Français{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-10">
        <!-- Success Header -->
        <div class="text-center mb-4">
            <div class="feature-icon mx-auto" style="background: linear-gradient(135deg, var(--success-color) 0%, #10b981 100%);">
                <i class="bi bi-check-circle"></i>
            </div>
            <h1 class="display-6 fw-bold text-success mb-2">Correction Terminée !</h1>
            <p class="lead text-muted">Voici le résultat de l'analyse de votre texte</p>
        </div>

        <!-- Comparison Cards -->
        <div class="row">
            <!-- Original Text -->
            <div class="col-lg-6 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-danger text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            Texte Original
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="text-comparison text-original">
                            <p class="mb-0">{{ texte_original }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Corrected Text -->
            <div class="col-lg-6 mb-4">
                <div class="card h-100">
                    <div class="card-header bg-success text-white">
                        <h5 class="mb-0">
                            <i class="bi bi-check-circle me-2"></i>
                            Texte Corrigé
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="text-comparison text-corrected">
                            <p class="mb-0">{{ texte_corrige }}</p>
                        </div>

                        <!-- Download Button -->
                        <div class="mt-3">
                            <a href="{% url 'telecharger_texte' %}?texte={{ texte_corrige|urlencode }}"
                               class="btn btn-outline-success">
                                <i class="bi bi-download me-2"></i>
                                Télécharger le texte corrigé
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="text-center mt-4">
            <div class="d-flex flex-wrap justify-content-center gap-3">
                <a href="{% url 'soumettre_texte' %}" class="btn btn-primary btn-lg">
                    <i class="bi bi-plus-circle me-2"></i>
                    Corriger un autre texte
                </a>

                <a href="{% url 'cv_upload' %}" class="btn btn-outline-primary btn-lg">
                    <i class="bi bi-file-earmark-text me-2"></i>
                    Corriger un CV
                </a>

                <a href="{% url 'historique' %}" class="btn btn-outline-secondary btn-lg">
                    <i class="bi bi-clock-history me-2"></i>
                    Voir l'historique
                </a>

                <a href="{% url 'export_csv' %}" class="btn btn-outline-info btn-lg">
                    <i class="bi bi-file-earmark-spreadsheet me-2"></i>
                    Export CSV
                </a>
            </div>
        </div>

        <!-- Statistics Card -->
        <div class="row mt-5">
            <div class="col-md-12">
                <div class="stats-card">
                    <h5><i class="bi bi-graph-up me-2"></i>Statistiques de Correction</h5>
                    <div class="row mt-3">
                        <div class="col-md-4">
                            <h6>Caractères Originaux</h6>
                            <h4>{{ texte_original|length }}</h4>
                        </div>
                        <div class="col-md-4">
                            <h6>Caractères Corrigés</h6>
                            <h4>{{ texte_corrige|length }}</h4>
                        </div>
                        <div class="col-md-4">
                            <h6>Différence</h6>
                            <h4>{% widthratio texte_corrige|length texte_original|length 100 %}%</h4>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
