from django.shortcuts import render
from django.http import HttpResponse
from .forms import TexteForm, CVUploadForm
from .models import Correction
import language_tool_python
import csv
import os
from PyPDF2 import PdfReader

#  Soumission de texte et correction
def soumettre_texte(request):
    texte_corrige = None
    if request.method == 'POST':
        form = TexteForm(request.POST)
        if form.is_valid():
            texte_original = form.cleaned_data['contenu']

            # Correction grammaticale avec LanguageTool (français)
            tool = language_tool_python.LanguageTool('fr')
            matches = tool.check(texte_original)
            texte_corrige = language_tool_python.utils.correct(texte_original, matches)

            # Sauvegarde dans la base de données 
            Correction.objects.create(
                texte_original=texte_original,
                texte_corrige=texte_corrige
            )

            return render(request, 'texte/resultat.html', {
                'texte_original': texte_original,
                'texte_corrige': texte_corrige
            })
    else:
        form = TexteForm()
    return render(request, 'texte/soumettre.html', {'form': form})

#  Correction de CV (upload PDF ou TXT)
def corriger_cv(request):
    texte_corrige = None
    texte_original = ""

    if request.method == 'POST':
        form = CVUploadForm(request.POST, request.FILES)
        if form.is_valid():
            fichier = form.cleaned_data['fichier_cv']
            extension = os.path.splitext(fichier.name)[1].lower()

            if extension == '.pdf':
                reader = PdfReader(fichier)
                for page in reader.pages:
                    texte_original += page.extract_text()
            elif extension == '.txt':
                texte_original = fichier.read().decode('utf-8')
            else:
                return HttpResponse("Format non supporté.")

            # Correction grammaticale avec LanguageTool (français)
            tool = language_tool_python.LanguageTool('fr')
            matches = tool.check(texte_original)
            texte_corrige = language_tool_python.utils.correct(texte_original, matches)

            return render(request, 'texte/cv_resultat.html', {
                'texte_original': texte_original,
                'texte_corrige': texte_corrige
            })
    else:
        form = CVUploadForm()

    return render(request, 'texte/cv_upload.html', {'form': form})

#  Téléchargement du texte corrigé
def telecharger_texte(request):
    texte = request.GET.get('texte', '')
    response = HttpResponse(texte, content_type='text/plain')
    response['Content-Disposition'] = 'attachment; filename="texte_corrige.txt"'
    return response

#  Historique des corrections
def historique(request):
    corrections = Correction.objects.all().order_by('-date_correction')
    return render(request, 'texte/historique.html', {'corrections': corrections})

#  Export CSV de l'historique
def export_csv(request):
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="corrections.csv"'
    writer = csv.writer(response)
    writer.writerow(['Texte original', 'Texte corrigé', 'Date de correction'])

    for correction in Correction.objects.all():
        writer.writerow([
            correction.texte_original,
            correction.texte_corrige,
            correction.date_correction
        ])
    return response
