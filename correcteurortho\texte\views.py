from django.shortcuts import render
from django.http import HttpResponse
from .forms import TexteForm, CVUploadForm
from .models import Correction
from .correction_service import FrenchCorrectionService
import csv
import os
from PyPDF2 import PdfReader
import logging

# Configuration du logging
logger = logging.getLogger(__name__)

# Instance globale du service de correction
correction_service = None

def get_correction_service():
    """Obtient une instance du service de correction (singleton)."""
    global correction_service
    if correction_service is None:
        try:
            correction_service = FrenchCorrectionService()
        except Exception as e:
            logger.error(f"Erreur lors de l'initialisation du service de correction: {e}")
            # Fallback vers LanguageTool simple
            import language_tool_python
            correction_service = language_tool_python.LanguageTool('fr')
    return correction_service

#  Soumission de texte et correction
def soumettre_texte(request):
    texte_corrige = None
    if request.method == 'POST':
        form = TexteForm(request.POST)
        if form.is_valid():
            texte_original = form.cleaned_data['contenu']

            try:
                # Utilisation du service de correction amélioré
                service = get_correction_service()

                if hasattr(service, 'correct_text'):
                    # Service de correction amélioré
                    texte_corrige = service.correct_text(texte_original)
                else:
                    # Fallback vers LanguageTool simple
                    matches = service.check(texte_original)
                    import language_tool_python
                    texte_corrige = language_tool_python.utils.correct(texte_original, matches)

                logger.info(f"Correction effectuée: {len(texte_original)} -> {len(texte_corrige)} caractères")

            except Exception as e:
                logger.error(f"Erreur lors de la correction: {e}")
                # En cas d'erreur, retourner le texte original
                texte_corrige = texte_original

            # Sauvegarde dans la base de données
            Correction.objects.create(
                texte_original=texte_original,
                texte_corrige=texte_corrige
            )

            return render(request, 'texte/resultat.html', {
                'texte_original': texte_original,
                'texte_corrige': texte_corrige
            })
    else:
        form = TexteForm()
    return render(request, 'texte/soumettre.html', {'form': form})

#  Correction de CV (upload PDF ou TXT)
def corriger_cv(request):
    texte_corrige = None
    texte_original = ""

    if request.method == 'POST':
        form = CVUploadForm(request.POST, request.FILES)
        if form.is_valid():
            fichier = form.cleaned_data['fichier_cv']
            extension = os.path.splitext(fichier.name)[1].lower()

            try:
                if extension == '.pdf':
                    reader = PdfReader(fichier)
                    for page in reader.pages:
                        texte_original += page.extract_text()
                elif extension == '.txt':
                    texte_original = fichier.read().decode('utf-8')
                else:
                    return HttpResponse("Format non supporté. Veuillez utiliser un fichier PDF ou TXT.")

                # Correction avec le service amélioré
                service = get_correction_service()

                if hasattr(service, 'correct_text'):
                    # Service de correction amélioré
                    texte_corrige = service.correct_text(texte_original)
                else:
                    # Fallback vers LanguageTool simple
                    matches = service.check(texte_original)
                    import language_tool_python
                    texte_corrige = language_tool_python.utils.correct(texte_original, matches)

                logger.info(f"Correction CV effectuée: {len(texte_original)} -> {len(texte_corrige)} caractères")

            except Exception as e:
                logger.error(f"Erreur lors de la correction du CV: {e}")
                # En cas d'erreur, retourner le texte original
                texte_corrige = texte_original if texte_original else "Erreur lors de l'extraction du texte."

            return render(request, 'texte/cv_resultat.html', {
                'texte_original': texte_original,
                'texte_corrige': texte_corrige
            })
    else:
        form = CVUploadForm()

    return render(request, 'texte/cv_upload.html', {'form': form})

#  Téléchargement du texte corrigé
def telecharger_texte(request):
    texte = request.GET.get('texte', '')
    response = HttpResponse(texte, content_type='text/plain')
    response['Content-Disposition'] = 'attachment; filename="texte_corrige.txt"'
    return response

#  Historique des corrections
def historique(request):
    corrections = Correction.objects.all().order_by('-date_correction')
    return render(request, 'texte/historique.html', {'corrections': corrections})

#  Export CSV de l'historique
def export_csv(request):
    response = HttpResponse(content_type='text/csv')
    response['Content-Disposition'] = 'attachment; filename="corrections.csv"'
    writer = csv.writer(response)
    writer.writerow(['Texte original', 'Texte corrigé', 'Date de correction'])

    for correction in Correction.objects.all():
        writer.writerow([
            correction.texte_original,
            correction.texte_corrige,
            correction.date_correction
        ])
    return response
